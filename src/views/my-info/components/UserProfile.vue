<script setup>
import { ref, computed } from 'vue'

const profile = ref({
  avatar: '', // Set to empty to test fallback
  nickname: '阿琦Aqi',
  userId: '21313133243243',
  phone: '13712341234',
  email: '<EMAIL>'
})

const showFallbackAvatar = computed(() => !profile.value.avatar)
const avatarInitial = computed(() => profile.value.nickname.charAt(0))
</script>

<template>
  <div class="user-profile-container">
    <div class="avatar-section">
      <div class="avatar-container">
        <img v-if="!showFallbackAvatar" :src="profile.avatar" alt="User Avatar" class="avatar-image" />
        <div v-else class="avatar-fallback">
          {{ avatarInitial }}
        </div>
      </div>
      <button class="upload-button">上传头像</button>
    </div>

    <form class="profile-form">
      <div class="form-group">
        <label for="nickname">昵称</label>
        <input type="text" id="nickname" v-model="profile.nickname" />
      </div>
      <div class="form-group">
        <label for="userId">用户ID</label>
        <input type="text" id="userId" v-model="profile.userId" readonly />
      </div>
      <div class="form-group">
        <label for="phone">手机号码</label>
        <input type="tel" id="phone" v-model="profile.phone" />
      </div>
      <div class="form-group">
        <label for="email">邮箱地址</label>
        <input type="email" id="email" v-model="profile.email" />
      </div>
    </form>

    <div class="action-buttons">
      <button class="save-button">保存</button>
      <button class="cancel-button">取消</button>
    </div>
  </div>
</template>

<style scoped>
.user-profile-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background-color: #ff5827;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 500;
}

.upload-button {
  padding: 8px 16px;
  border-radius: 217px;
  border: 1px solid #0057d9;
  background-color: #ffffff;
  color: #0057d9;
  font-size: 14px;
  cursor: pointer;
}

.profile-form {
  display: grid;
  grid-template-columns: repeat(2, 280px);
  gap: 24px;
  margin-top: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group label {
  font-size: 14px;
  color: #595e66;
}

.form-group input {
  height: 36px;
  border-radius: 6px;
  border: 1px solid #dcdcdc;
  background-color: #ffffff;
  padding: 0 12px;
  font-size: 14px;
  color: #222529;
  width: 100%;
}

.form-group input[readonly] {
  background-color: #f4f4f4;
  color: #595e66;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.save-button,
.cancel-button {
  width: 100px;
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.save-button {
  background-color: #0057d9;
  color: #ffffff;
  border: none;
}

.cancel-button {
  background-color: #ffffff;
  color: #222529;
  border: 1px solid #d8d8d8;
}
</style>
