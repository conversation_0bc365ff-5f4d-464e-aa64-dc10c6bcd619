<script setup>
import { ref, provide } from 'vue'
import HistoryPanel from './components/HistoryPanel.vue'
import FileUpload from './components/FileUpload.vue'

const historyPanelRef = ref()

// 提供刷新对话列表的方法给子组件
const refreshConversations = () => {
  if (historyPanelRef.value) {
    historyPanelRef.value.refreshConversations()
  }
}

provide('refreshConversations', refreshConversations)
</script>

<script>
export default {
  name: 'file-check',
}
</script>
<template>
  <div style="display: flex; width: 100%; height: 100%">
    <HistoryPanel ref="historyPanelRef" />
    <FileUpload />
  </div>
</template>
