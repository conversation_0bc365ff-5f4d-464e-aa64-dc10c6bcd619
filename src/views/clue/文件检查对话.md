---
title: 法律联盟
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 法律联盟

Base URLs:

# Authentication

* API Key (Authorization)
    - Parameter Name: **Authorization**, in: header. 

- HTTP Authentication, scheme: bearer

# 用户 APP - 法律文书检查对话

<a id="opIdupdateCheckConversation"></a>

## PUT 更新法律文书检查对话

PUT /app-api/law/check/conversation/update

> Body 请求参数

```json
{
  "id": 15651,
  "userId": 19100,
  "type": "1-判决书，2-处罚决定书，3-租赁权拍卖公告，4-合同",
  "title": "string",
  "pinned": true,
  "pinnedTime": "2019-08-24T14:15:22Z"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[CheckConversationSaveReqVO](#schemacheckconversationsavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIdcreateCheckConversation"></a>

## POST 创建法律文书检查对话

POST /app-api/law/check/conversation/create

> Body 请求参数

```json
{
  "type": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[CheckConversationSaveReqVO](#schemacheckconversationsavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultLong](#schemacommonresultlong)|

<a id="opIdgetCheckConversationPage"></a>

## GET 获得法律文书检查对话分页

GET /app-api/law/check/conversation/page

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|userId|query|integer(int64)| 否 |用户编号|
|type|query|string| 否 |类型：|
|title|query|string| 否 |对话标题|
|pinned|query|boolean| 否 |是否置顶|
|pinnedTime|query|array[string]| 否 |置顶时间|
|createTime|query|array[string]| 否 |创建时间|
|pageNo|query|integer(int32)| 是 |页码，从 1 开始|
|pageSize|query|integer(int32)| 是 |每页条数，最大值为 100|

> 返回示例

> 200 Response

```
{"code":0,"data":{"list":[{"id":15651,"userId":19100,"type":2,"title":"string","pinned":true,"pinnedTime":"2019-08-24T14:15:22Z","createTime":"2019-08-24T14:15:22Z"}],"total":0},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultPageResultCheckConversationRespVO](#schemacommonresultpageresultcheckconversationrespvo)|

<a id="opIdgetConversationMyList"></a>

## GET 获得【我的】对话列表

GET /app-api/law/check/conversation/my-list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|query|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":[{"id":15651,"userId":19100,"type":2,"title":"string","pinned":true,"pinnedTime":"2019-08-24T14:15:22Z","createTime":"2019-08-24T14:15:22Z"}],"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultListCheckConversationRespVO](#schemacommonresultlistcheckconversationrespvo)|

<a id="opIdgetCheckConversation"></a>

## GET 获得法律文书检查对话

GET /app-api/law/check/conversation/get

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |编号|

> 返回示例

> 200 Response

```
{"code":0,"data":{"id":15651,"userId":19100,"type":2,"title":"string","pinned":true,"pinnedTime":"2019-08-24T14:15:22Z","createTime":"2019-08-24T14:15:22Z"},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultCheckConversationRespVO](#schemacommonresultcheckconversationrespvo)|

<a id="opIdexportCheckConversationExcel"></a>

## GET 导出法律文书检查对话 Excel

GET /app-api/law/check/conversation/export-excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|userId|query|integer(int64)| 否 |用户编号|
|type|query|string| 否 |类型：|
|title|query|string| 否 |对话标题|
|pinned|query|boolean| 否 |是否置顶|
|pinnedTime|query|array[string]| 否 |置顶时间|
|createTime|query|array[string]| 否 |创建时间|
|pageNo|query|integer(int32)| 是 |页码，从 1 开始|
|pageSize|query|integer(int32)| 是 |每页条数，最大值为 100|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIddeleteCheckConversation"></a>

## DELETE 删除法律文书检查对话

DELETE /app-api/law/check/conversation/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |编号|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIddeleteCheckConversationList"></a>

## DELETE 批量删除法律文书检查对话

DELETE /app-api/law/check/conversation/delete-list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

# 数据模型

<h2 id="tocS_CommonResultBoolean">CommonResultBoolean</h2>

<a id="schemacommonresultboolean"></a>
<a id="schema_CommonResultBoolean"></a>
<a id="tocScommonresultboolean"></a>
<a id="tocscommonresultboolean"></a>

```json
{
  "code": 0,
  "data": true,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|boolean|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CheckConversationSaveReqVO">CheckConversationSaveReqVO</h2>

<a id="schemacheckconversationsavereqvo"></a>
<a id="schema_CheckConversationSaveReqVO"></a>
<a id="tocScheckconversationsavereqvo"></a>
<a id="tocscheckconversationsavereqvo"></a>

```json
{
  "id": 15651,
  "userId": 19100,
  "type": "1-判决书，2-处罚决定书，3-租赁权拍卖公告，4-合同",
  "title": "string",
  "pinned": true,
  "pinnedTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 法律文书检查对话新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||对话编号|
|userId|integer(int64)|true|none||用户编号|
|type|string|false|none||类型：|
|title|string|false|none||对话标题|
|pinned|boolean|false|none||是否置顶|
|pinnedTime|string(date-time)|false|none||置顶时间|

<h2 id="tocS_CommonResultLong">CommonResultLong</h2>

<a id="schemacommonresultlong"></a>
<a id="schema_CommonResultLong"></a>
<a id="tocScommonresultlong"></a>
<a id="tocscommonresultlong"></a>

```json
{
  "code": 0,
  "data": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|integer(int64)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CheckConversationRespVO">CheckConversationRespVO</h2>

<a id="schemacheckconversationrespvo"></a>
<a id="schema_CheckConversationRespVO"></a>
<a id="tocScheckconversationrespvo"></a>
<a id="tocscheckconversationrespvo"></a>

```json
{
  "id": 15651,
  "userId": 19100,
  "type": 2,
  "title": "string",
  "pinned": true,
  "pinnedTime": "2019-08-24T14:15:22Z",
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 法律文书检查对话 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||对话编号|
|userId|integer(int64)|true|none||用户编号|
|type|string|false|none||类型：|
|title|string|false|none||对话标题|
|pinned|boolean|false|none||是否置顶|
|pinnedTime|string(date-time)|false|none||置顶时间|
|createTime|string(date-time)|false|none||创建时间|

<h2 id="tocS_CommonResultPageResultCheckConversationRespVO">CommonResultPageResultCheckConversationRespVO</h2>

<a id="schemacommonresultpageresultcheckconversationrespvo"></a>
<a id="schema_CommonResultPageResultCheckConversationRespVO"></a>
<a id="tocScommonresultpageresultcheckconversationrespvo"></a>
<a id="tocscommonresultpageresultcheckconversationrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 15651,
        "userId": 19100,
        "type": 2,
        "title": "string",
        "pinned": true,
        "pinnedTime": "2019-08-24T14:15:22Z",
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultCheckConversationRespVO](#schemapageresultcheckconversationrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultCheckConversationRespVO">PageResultCheckConversationRespVO</h2>

<a id="schemapageresultcheckconversationrespvo"></a>
<a id="schema_PageResultCheckConversationRespVO"></a>
<a id="tocSpageresultcheckconversationrespvo"></a>
<a id="tocspageresultcheckconversationrespvo"></a>

```json
{
  "list": [
    {
      "id": 15651,
      "userId": 19100,
      "type": 2,
      "title": "string",
      "pinned": true,
      "pinnedTime": "2019-08-24T14:15:22Z",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[CheckConversationRespVO](#schemacheckconversationrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultCheckConversationRespVO">CommonResultCheckConversationRespVO</h2>

<a id="schemacommonresultcheckconversationrespvo"></a>
<a id="schema_CommonResultCheckConversationRespVO"></a>
<a id="tocScommonresultcheckconversationrespvo"></a>
<a id="tocscommonresultcheckconversationrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 15651,
    "userId": 19100,
    "type": 2,
    "title": "string",
    "pinned": true,
    "pinnedTime": "2019-08-24T14:15:22Z",
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[CheckConversationRespVO](#schemacheckconversationrespvo)|false|none||管理后台 - 法律文书检查对话 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListCheckConversationRespVO">CommonResultListCheckConversationRespVO</h2>

<a id="schemacommonresultlistcheckconversationrespvo"></a>
<a id="schema_CommonResultListCheckConversationRespVO"></a>
<a id="tocScommonresultlistcheckconversationrespvo"></a>
<a id="tocscommonresultlistcheckconversationrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 15651,
      "userId": 19100,
      "type": 2,
      "title": "string",
      "pinned": true,
      "pinnedTime": "2019-08-24T14:15:22Z",
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[CheckConversationRespVO](#schemacheckconversationrespvo)]|false|none||[管理后台 - 法律文书检查对话 Response VO]|
|msg|string|false|none||none|

