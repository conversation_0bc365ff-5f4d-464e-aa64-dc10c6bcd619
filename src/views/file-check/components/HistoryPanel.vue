<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Search, Calendar, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getMyConversationList, createConversation } from '@/api/checkTask'

const route = useRoute()

const searchQuery = ref('')
const dateRange = ref('')
const historyItems = ref([])
const loading = ref(false)
const selectedConversationId = ref(null)

// 当前文书类型
const currentType = computed(() => {
  const type = route.query.type || '0'
  return String(parseInt(type) + 1) // URL中0-3对应API中1-4
})

// 获取对话列表
const fetchConversations = async () => {
  try {
    loading.value = true
    const response = await getMyConversationList(currentType.value)
    historyItems.value = response.data.map((item) => ({
      id: item.id,
      name: item.title,
      time: formatTime(item.createTime),
      pinned: item.pinned,
      type: item.type,
    }))
  } catch (error) {
    console.error('获取对话列表失败:', error)
    ElMessage.error('获取历史记录失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 新建对话
const createNewConversation = async () => {
  try {
    await createConversation(parseInt(currentType.value))
    ElMessage.success('新建对话成功')
    // 刷新对话列表
    await fetchConversations()
  } catch (error) {
    console.error('创建对话失败:', error)
    ElMessage.error('创建对话失败')
  }
}

// 选择对话
const selectConversation = (conversationId) => {
  selectedConversationId.value = conversationId
  // 通知父组件切换到对应的任务列表
  emit('conversation-selected', conversationId)
}

// 监听类型变化，重新获取数据
watch(
  currentType,
  () => {
    fetchConversations()
    selectedConversationId.value = null // 重置选中状态
  },
  { immediate: true },
)

onMounted(() => {
  fetchConversations()
})

// 定义事件
const emit = defineEmits(['conversation-selected'])

// 暴露方法给父组件
defineExpose({
  refreshConversations: fetchConversations,
})
</script>

<template>
  <div class="history-panel">
    <div class="header fixed-header">
      <div class="title-container">
        <div class="title">
          <img class="icon" src="@/assets/icons/time.png" alt="History Icon" />
          <h2>历史记录</h2>
        </div>
        <button class="new-chat-btn" @click="createNewConversation">
          <img src="@/assets/icons/new-chat.svg" alt="New Chat" />
          <span>新建对话</span>
        </button>
      </div>
      <el-input v-model="searchQuery" class="search-input" placeholder="搜索">
        <template #prefix>
          <el-icon><search /></el-icon>
        </template>
      </el-input>
      <el-date-picker
        style="width: 273px"
        v-model="dateRange"
        type="daterange"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="date-picker"
        :prefix-icon="Calendar"
      />
    </div>
    <ul class="history-list scroll-container">
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      <div v-else-if="historyItems.length === 0" class="empty-container">
        <span>暂无历史记录</span>
      </div>
      <li
        v-else
        v-for="item in historyItems"
        :key="item.id"
        class="history-item"
        :class="{ selected: selectedConversationId === item.id }"
        @click="selectConversation(item.id)"
      >
        <div class="item-content">
          <span class="item-name" :title="item.name">{{ item.name }}</span>
          <span class="item-time">{{ item.time }}</span>
        </div>
        <div v-if="item.pinned" class="pinned-icon">📌</div>
      </li>
    </ul>
  </div>
</template>

<style scoped>
.history-panel {
  width: 320px;
  height: 100%;
  border-right: 1px solid #f0f1f2;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding: 24px 23px;
  box-sizing: border-box;
}

.header {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #222529;
}

.icon {
  width: 16px;
  height: 16px;
}

h2 {
  margin: 0;
  font-size: inherit;
  font-weight: inherit;
}

.new-chat-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 400;
  color: #0057d9;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.new-chat-btn img {
  width: 15px;
  height: 15px;
}

.search-input {
  margin-top: 8px;
}

.search-input :deep(.el-input__wrapper),
.date-picker :deep(.el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #d8d8d8;
  box-shadow: none;
}

.date-picker {
  width: 100%;
}

.el-icon {
  color: #7f8792;
}

.history-list {
  list-style: none;
  padding: 0;
  margin: 16px 0 0;
  display: flex;
  flex-direction: column;
}

.history-item {
  border-top: 1px solid #f0f1f2;
  background-color: #ffffff;
  padding: 11px 7px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-bottom: -1px; /* To overlap borders */
}

.history-item:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.history-item:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  margin-bottom: 0;
}

.history-item:hover {
  background-color: #f8f9fa;
}

.history-item.selected {
  background: #e5f0ff;
}

.history-item.selected .item-name {
  font-size: 14px;
  font-weight: 500;
  color: #0057d9;
}

.item-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  gap: 4px;
}

.item-name {
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  color: #222529;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-time {
  font-size: 12px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  color: #7f8792;
}

.pinned-icon {
  margin-left: 8px;
  font-size: 12px;
  color: #0057d9;
  flex-shrink: 0;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #8b949e;
  font-size: 14px;
}

.loading-container .el-icon {
  font-size: 20px;
  margin-bottom: 8px;
}
</style>
