<template>
  <div ref="userProfileRef" class="user-profile">
    <div class="avatar" :style="user.avatar ? { backgroundImage: 'url(' + user.avatar + ')' } : {}">
      <span v-if="!user.avatar">{{ user.name.charAt(0) }}</span>
    </div>
    <span class="username">{{ user.name }}</span>
    <button class="options-button" @click="toggleUserMenu" :class="{ active: isUserMenuVisible }">
      <span></span>
      <span></span>
      <span></span>
    </button>

    <!-- 用户下拉菜单 -->
    <UserDropdownMenu
      :visible="isUserMenuVisible"
      @purchase="handlePurchase"
      @logout="handleLogout"
      @close="closeUserMenu"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import UserDropdownMenu from '@/components/UserDropdownMenu.vue'

const userStore = useUserStore()

// 从 store 获取用户信息
const user = computed(() => ({
  name: userStore.userInfo.name || userStore.userInfo.nickname || '用户',
  avatar: userStore.userInfo.avatar || '',
}))

// 用户下拉菜单状态
const isUserMenuVisible = ref(false)
const userProfileRef = ref(null)

// 切换用户菜单显示状态
const toggleUserMenu = () => {
  isUserMenuVisible.value = !isUserMenuVisible.value
}

// 关闭用户菜单
const closeUserMenu = () => {
  isUserMenuVisible.value = false
}

// 处理购买套餐
const handlePurchase = () => {
  console.log('跳转到购买套餐页面')
  // 这里可以添加路由跳转或打开购买对话框的逻辑
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await userStore.logout()
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  if (userProfileRef.value && !userProfileRef.value.contains(event.target)) {
    closeUserMenu()
  }
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* Styles are from the original sidebar user profile */
.user-profile {
  position: relative;
  display: flex;
  align-items: center;
  height: 60px;
  padding: 0 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.user-profile:hover {
  background-color: #f0f1f2;
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #f66826;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.username {
  margin-left: 10px;
  font-weight: 500;
  color: #222529;
}

.options-button {
  margin-left: auto;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  gap: 3px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.options-button:hover {
  background-color: #e5e7eb;
}

.options-button span {
  width: 3px;
  height: 3px;
  background-color: #222529;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.options-button:hover span,
.options-button.active span {
  background-color: #0057d9;
}
</style>
