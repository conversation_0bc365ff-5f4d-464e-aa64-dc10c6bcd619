---
title: 法律联盟
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 法律联盟

Base URLs:

# Authentication

* API Key (Authorization)
    - Parameter Name: **Authorization**, in: header. 

# 管理后台 - 用户个人中心

<a id="opIdupdateUserProfile"></a>

## PUT 修改用户个人信息

PUT /admin-api/system/user/profile/update

> Body 请求参数

```json
{
  "nickname": "芋艿",
  "email": "<EMAIL>",
  "mobile": 15601691300,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/1.png"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[UserProfileUpdateReqVO](#schemauserprofileupdatereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIdupdateUserProfilePassword"></a>

## PUT 修改用户个人密码

PUT /admin-api/system/user/profile/update-password

> Body 请求参数

```json
{
  "oldPassword": 123456,
  "newPassword": 654321
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[UserProfileUpdatePasswordReqVO](#schemauserprofileupdatepasswordreqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIdgetUserProfile"></a>

## GET 获得登录用户信息

GET /admin-api/system/user/profile/get

> 返回示例

> 200 Response

```
{"code":0,"data":{"id":1,"username":"yudao","nickname":"芋艿","email":"<EMAIL>","mobile":15601691300,"sex":1,"avatar":"https://www.iocoder.cn/xxx.png","loginIp":"***********","loginDate":"时间戳格式","createTime":"时间戳格式","roles":[{"id":1024,"name":"芋道"}],"dept":{"id":1024,"name":"芋道","parentId":1024},"posts":[{"id":1024,"name":"小土豆"}]},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultUserProfileRespVO](#schemacommonresultuserprofilerespvo)|

# 数据模型

<h2 id="tocS_CommonResultBoolean">CommonResultBoolean</h2>

<a id="schemacommonresultboolean"></a>
<a id="schema_CommonResultBoolean"></a>
<a id="tocScommonresultboolean"></a>
<a id="tocscommonresultboolean"></a>

```json
{
  "code": 0,
  "data": true,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|boolean|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_UserProfileUpdateReqVO">UserProfileUpdateReqVO</h2>

<a id="schemauserprofileupdatereqvo"></a>
<a id="schema_UserProfileUpdateReqVO"></a>
<a id="tocSuserprofileupdatereqvo"></a>
<a id="tocsuserprofileupdatereqvo"></a>

```json
{
  "nickname": "芋艿",
  "email": "<EMAIL>",
  "mobile": 15601691300,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/1.png"
}

```

管理后台 - 用户个人信息更新 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|nickname|string|false|none||用户昵称|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|
|avatar|string|false|none||角色头像|

<h2 id="tocS_UserProfileUpdatePasswordReqVO">UserProfileUpdatePasswordReqVO</h2>

<a id="schemauserprofileupdatepasswordreqvo"></a>
<a id="schema_UserProfileUpdatePasswordReqVO"></a>
<a id="tocSuserprofileupdatepasswordreqvo"></a>
<a id="tocsuserprofileupdatepasswordreqvo"></a>

```json
{
  "oldPassword": 123456,
  "newPassword": 654321
}

```

管理后台 - 用户个人中心更新密码 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|oldPassword|string|true|none||旧密码|
|newPassword|string|true|none||新密码|

<h2 id="tocS_CommonResultUserProfileRespVO">CommonResultUserProfileRespVO</h2>

<a id="schemacommonresultuserprofilerespvo"></a>
<a id="schema_CommonResultUserProfileRespVO"></a>
<a id="tocScommonresultuserprofilerespvo"></a>
<a id="tocscommonresultuserprofilerespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "yudao",
    "nickname": "芋艿",
    "email": "<EMAIL>",
    "mobile": 15601691300,
    "sex": 1,
    "avatar": "https://www.iocoder.cn/xxx.png",
    "loginIp": "***********",
    "loginDate": "时间戳格式",
    "createTime": "时间戳格式",
    "roles": [
      {
        "id": 1024,
        "name": "芋道"
      }
    ],
    "dept": {
      "id": 1024,
      "name": "芋道",
      "parentId": 1024
    },
    "posts": [
      {
        "id": 1024,
        "name": "小土豆"
      }
    ]
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[UserProfileRespVO](#schemauserprofilerespvo)|false|none||管理后台 - 用户个人中心信息 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_DeptSimpleRespVO">DeptSimpleRespVO</h2>

<a id="schemadeptsimplerespvo"></a>
<a id="schema_DeptSimpleRespVO"></a>
<a id="tocSdeptsimplerespvo"></a>
<a id="tocsdeptsimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道",
  "parentId": 1024
}

```

管理后台 - 部门精简信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||部门编号|
|name|string|true|none||部门名称|
|parentId|integer(int64)|true|none||父部门 ID|

<h2 id="tocS_PostSimpleRespVO">PostSimpleRespVO</h2>

<a id="schemapostsimplerespvo"></a>
<a id="schema_PostSimpleRespVO"></a>
<a id="tocSpostsimplerespvo"></a>
<a id="tocspostsimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "小土豆"
}

```

管理后台 - 岗位信息的精简 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||岗位序号|
|name|string|true|none||岗位名称|

<h2 id="tocS_RoleSimpleRespVO">RoleSimpleRespVO</h2>

<a id="schemarolesimplerespvo"></a>
<a id="schema_RoleSimpleRespVO"></a>
<a id="tocSrolesimplerespvo"></a>
<a id="tocsrolesimplerespvo"></a>

```json
{
  "id": 1024,
  "name": "芋道"
}

```

管理后台 - 角色精简信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||角色编号|
|name|string|true|none||角色名称|

<h2 id="tocS_UserProfileRespVO">UserProfileRespVO</h2>

<a id="schemauserprofilerespvo"></a>
<a id="schema_UserProfileRespVO"></a>
<a id="tocSuserprofilerespvo"></a>
<a id="tocsuserprofilerespvo"></a>

```json
{
  "id": 1,
  "username": "yudao",
  "nickname": "芋艿",
  "email": "<EMAIL>",
  "mobile": 15601691300,
  "sex": 1,
  "avatar": "https://www.iocoder.cn/xxx.png",
  "loginIp": "***********",
  "loginDate": "时间戳格式",
  "createTime": "时间戳格式",
  "roles": [
    {
      "id": 1024,
      "name": "芋道"
    }
  ],
  "dept": {
    "id": 1024,
    "name": "芋道",
    "parentId": 1024
  },
  "posts": [
    {
      "id": 1024,
      "name": "小土豆"
    }
  ]
}

```

管理后台 - 用户个人中心信息 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户编号|
|username|string|true|none||用户账号|
|nickname|string|true|none||用户昵称|
|email|string|false|none||用户邮箱|
|mobile|string|false|none||手机号码|
|sex|integer(int32)|false|none||用户性别，参见 SexEnum 枚举类|
|avatar|string|false|none||用户头像|
|loginIp|string|true|none||最后登录 IP|
|loginDate|string(date-time)|true|none||最后登录时间|
|createTime|string(date-time)|true|none||创建时间|
|roles|[[RoleSimpleRespVO](#schemarolesimplerespvo)]|false|none||[管理后台 - 角色精简信息 Response VO]|
|dept|[DeptSimpleRespVO](#schemadeptsimplerespvo)|false|none||管理后台 - 部门精简信息 Response VO|
|posts|[[PostSimpleRespVO](#schemapostsimplerespvo)]|false|none||[管理后台 - 岗位信息的精简 Response VO]|

