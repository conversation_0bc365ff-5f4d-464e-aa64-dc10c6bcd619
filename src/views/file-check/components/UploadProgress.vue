<script setup>
import { computed } from 'vue';
import loadingIcon from '@/assets/icons/loading.svg';
import completedIcon from '@/assets/icons/upload-done.svg';
import SimilarCases from './SimilarCases.vue';

const props = defineProps({
  fileName: {
    type: String,
    default: '20250701XX法院诉讼文件.pdf',
  },
  fileSize: {
    type: String,
    default: '3.2 MB',
  },
  progress: {
    type: Number,
    default: 50,
  },
  status: {
    type: String,
    default: 'uploading', // 'uploading', 'completed', 'analyzing', 'analyzed'
  },
  similarCases: {
    type: Array,
    default: () => [],
  },
});

const isUploading = computed(() => props.status === 'uploading');
const isCompleted = computed(() => props.status === 'completed');
const isAnalyzing = computed(() => props.status === 'analyzing');
const isAnalyzed = computed(() => props.status === 'analyzed');

const progressStyle = computed(() => ({
  width: `${props.progress}%`,
  backgroundColor: isCompleted.value || isAnalyzed.value ? '#28a745' : '#0057d9',
}));

const statusText = computed(() => {
  if (isUploading.value) return '正在上传';
  if (isCompleted.value) return '上传完成';
  if (isAnalyzing.value) return '正在智能分析中...';
  if (isAnalyzed.value) return '分析完成';
  return '';
});

const statusIcon = computed(() => {
  if (isUploading.value || isAnalyzing.value) return loadingIcon;
  if (isCompleted.value || isAnalyzed.value) return completedIcon;
  return '';
});

const actionButtonText = computed(() => {
  if (isUploading.value || isCompleted.value) return '提交';
  if (isAnalyzing.value || isAnalyzed.value) return '下载报告';
  return '';
});

const isActionButtonDisabled = computed(() => {
  return isUploading.value || isAnalyzing.value;
});
</script>

<template>
  <div class="upload-item">
    <div class="main-content">
      <div class="content-wrapper">
        <div class="top-row">
          <div class="file-info">
            <div class="file-icon">
              <img src="@/assets/icons/pdf.svg" alt="PDF Icon" />
            </div>
            <div class="file-details">
              <span class="file-name">{{ fileName }}</span>
              <span class="file-size">{{ fileSize }}</span>
            </div>
          </div>
        </div>
        <div class="bottom-row">
          <div class="progress-bar-container">
            <div class="progress-bar" :style="progressStyle"></div>
          </div>
          <div class="status-details">
            <span class="progress-percentage">{{ progress }}%</span>
            <div class="upload-status">
              <img :src="statusIcon" :class="{ 'icon-loading': isUploading || isAnalyzing }" alt="Status Icon" />
              <span>{{ statusText }}</span>
            </div>
            <button v-if="!isAnalyzed" class="btn btn-danger">删除</button>
          </div>
        </div>
        <SimilarCases v-if="isAnalyzed || isAnalyzing" :cases="similarCases" />
      </div>
      <div class="separator"></div>
      <div class="action-area">
        <button class="action-button" :disabled="isActionButtonDisabled">
          {{ actionButtonText }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.upload-item {
  border-radius: 8px;
  background-color: #f8f9fa;
  padding: 16px;
}

.main-content {
  display: flex;
  align-items: center;
}

.content-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.top-row,
.bottom-row {
  display: flex;
  align-items: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border: 1px solid #ebebeb;
  border-radius: 8px;
}

.file-icon img {
  width: 28px;
  height: 28px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #222529;
}

.file-size {
  font-size: 12px;
  color: #7f8792;
}

.bottom-row {
  gap: 16px;
}

.progress-bar-container {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  width: 80%;
}

.progress-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.status-details {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #7f8792;
}

.progress-percentage {
  font-weight: 500;
  display: inline-block;
  width: 36px;
  flex-shrink: 0;
  color: #222529;
}

.upload-status {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 114px;
  gap: 4px;
}

.upload-status img {
  width: 14px;
  height: 14px;
}

.btn-danger {
  background-color: transparent;
  color: #f5222d;
  border-color: transparent;
  padding: 4px 8px;
  font-size: 12px;
}

.btn-danger:hover {
  background-color: #fff1f0;
  border-color: #ffa39e;
}

.separator {
  width: 1px;
  height: 60px;
  background-color: #e9ecef;
  margin: 0 24px;
}

.action-area {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button {
  width: 80px;
  height: 32px;
  border-radius: 217px;
  border: 1px solid #0057d9;
  background-color: #fff;
  color: #0057d9;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.action-button:disabled {
  border-color: #bfccdb;
  color: #bfccdb;
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.action-button:not(:disabled):hover {
  background-color: #0057d9;
  color: #fff;
}
</style>
