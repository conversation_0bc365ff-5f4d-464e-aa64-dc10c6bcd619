<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElPagination } from 'element-plus'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 从用户store获取数据
const username = computed(() => userStore.userInfo.nickname || '用户')
const avatarSrc = computed(() => userStore.userInfo.avatar || '')
const remainingCount = computed(() => userStore.userInfo.point || 0)

const showFallbackAvatar = computed(() => !avatarSrc.value)
const avatarInitial = computed(() => username.value.charAt(0))

const orderData = ref([
  {
    orderId: '202407081207021629',
    packageName: '20次套餐',
    price: '¥998.00',
    discount: '优惠¥20',
    actualPayment: '¥978.00',
    status: '已完成',
    paymentMethod: '支付宝',
    purchaseTime: '2025-07-18 18:01:26',
  },
  {
    orderId: '202309251519030359',
    packageName: '20次套餐',
    price: '¥998.00',
    discount: '优惠¥20',
    actualPayment: '¥978.00',
    status: '已完成',
    paymentMethod: '微信支付',
    purchaseTime: '2024-07-18 18:01:26',
  },
  {
    orderId: '202309251519030360',
    packageName: '10次套餐',
    price: '¥498.00',
    discount: '',
    actualPayment: '¥498.00',
    status: '已完成',
    paymentMethod: '支付宝',
    purchaseTime: '2024-07-17 12:00:00',
  },
  {
    orderId: '202309251519030361',
    packageName: '30次套餐',
    price: '¥1498.00',
    discount: '优惠¥50',
    actualPayment: '¥1448.00',
    status: '已完成',
    paymentMethod: '微信支付',
    purchaseTime: '2024-07-16 11:00:00',
  },
  {
    orderId: '202309251519030362',
    packageName: '50次套餐',
    price: '¥2498.00',
    discount: '',
    actualPayment: '¥2498.00',
    status: '已完成',
    paymentMethod: '支付宝',
    purchaseTime: '2024-07-15 10:00:00',
  },
  {
    orderId: '202309251519030363',
    packageName: '100次套餐',
    price: '¥4998.00',
    discount: '优惠¥100',
    actualPayment: '¥4898.00',
    status: '已完成',
    paymentMethod: '微信支付',
    purchaseTime: '2024-07-14 09:00:00',
  },
  {
    orderId: '202309251519030364',
    packageName: '20次套餐',
    price: '¥998.00',
    discount: '',
    actualPayment: '¥998.00',
    status: '已完成',
    paymentMethod: '支付宝',
    purchaseTime: '2024-07-13 08:00:00',
  },
  {
    orderId: '202309251519030365',
    packageName: '10次套餐',
    price: '¥498.00',
    discount: '优惠¥10',
    actualPayment: '¥488.00',
    status: '已完成',
    paymentMethod: '微信支付',
    purchaseTime: '2024-07-12 07:00:00',
  },
  {
    orderId: '202309251519030366',
    packageName: '30次套餐',
    price: '¥1498.00',
    discount: '',
    actualPayment: '¥1498.00',
    status: '已完成',
    paymentMethod: '支付宝',
    purchaseTime: '2024-07-11 06:00:00',
  },
  {
    orderId: '202309251519030367',
    packageName: '50次套餐',
    price: '¥2498.00',
    discount: '优惠¥50',
    actualPayment: '¥2448.00',
    status: '已完成',
    paymentMethod: '微信支付',
    purchaseTime: '2024-07-10 05:00:00',
  },
  {
    orderId: '202309251519030368',
    packageName: '100次套餐',
    price: '¥4998.00',
    discount: '',
    actualPayment: '¥4998.00',
    status: '已完成',
    paymentMethod: '支付宝',
    purchaseTime: '2024-07-09 04:00:00',
  },
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => orderData.value.length)

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return orderData.value.slice(start, end)
})

function handleSizeChange(val) {
  pageSize.value = val
}

function handleCurrentChange(page) {
  currentPage.value = page
}

// 组件挂载时获取用户信息
onMounted(async () => {
  if (userStore.isLoggedIn && !userStore.userInfo.id) {
    try {
      await userStore.fetchUserProfile()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})
</script>

<template>
  <div class="order-info-container">
    <div class="profile-summary">
      <div class="avatar-container">
        <img v-if="!showFallbackAvatar" class="avatar" :src="avatarSrc" />
        <div v-else class="avatar-fallback">
          {{ avatarInitial }}
        </div>
      </div>
      <div class="profile-details">
        <span class="name">{{ username }}</span>
        <span class="download-quota">剩余下载次数：{{ remainingCount }}次</span>
      </div>
      <button class="purchase-button">购买下载次数</button>
    </div>

    <h2 class="section-title">订单明细</h2>

    <div class="table-wrapper">
      <el-table
        :data="paginatedData"
        style="width: 100%"
        height="100%"
        header-row-class-name="table-header-row"
        :row-style="{ height: '64px' }"
      >
        <el-table-column prop="orderId" label="订单编号" width="220" />
        <el-table-column prop="packageName" label="套餐名称" />
        <el-table-column label="套餐价格">
          <template #default="{ row }">
            {{ row.price }} <span class="discount">{{ row.discount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="actualPayment" label="实付款" />
        <el-table-column prop="status" label="订单状态" />
        <el-table-column prop="paymentMethod" label="支付方式" />
        <el-table-column prop="purchaseTime" label="购买时间" width="180" />
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next"
        :total="total"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        :current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.order-info-container {
  display: flex;
  flex-direction: column;
  gap: 25px;
  height: 100%;
}

.profile-summary {
  display: flex;
  align-items: center;
  gap: 13px;
}

.avatar-container {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden; /* Ensures the image stays within the rounded corners */
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background-color: #ff5827;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 500;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 13px;
}

.name {
  font-size: 18px;
  font-weight: 500;
  color: #222529;
}

.download-quota {
  font-size: 14px;
  color: #7f8792;
}

.purchase-button {
  border-radius: 217px;
  border: 1px solid #0057d9;
  background-color: #ffffff;
  font-size: 14px;
  color: #0057d9;
  padding: 8px 16px;
  cursor: pointer;
  margin-left: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #222529;
  margin: 0;
  margin-top: 15px;
}

.discount {
  color: #7f8792;
  margin-left: 8px;
}

.table-wrapper {
  flex: 1;
  min-height: 0;
}

.pagination-container {
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
/* Global style for header */
.el-table .table-header-row th {
  background-color: #f7f9fa !important;
  color: #595e66;
  font-weight: 400;
}

.el-table .el-table__header-wrapper {
  border-radius: 8px;
}

.el-table,
.el-table__expanded-cell {
  background-color: transparent;
}

.el-table th,
.el-table tr {
  background-color: transparent;
}

.el-table td,
.el-table th.is-leaf {
  border-bottom: 1px solid #f0f1f2;
}

.el-table--border::after,
.el-table--group::after,
.el-table::before {
  content: none;
}

.el-pagination.is-background .el-pager li.is-active {
  background-color: #2457d9 !important;
}
</style>
