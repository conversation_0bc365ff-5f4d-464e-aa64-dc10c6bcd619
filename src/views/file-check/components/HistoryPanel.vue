<script setup>
import { ref } from 'vue'
import { Search, Calendar } from '@element-plus/icons-vue'

const searchQuery = ref('')
const dateRange = ref('')

const historyItems = ref([
  { id: 1, name: '案件诉讼名称01', time: '2024-10-10 17:51:45' },
  { id: 2, name: '案件诉讼名称02', time: '2024-10-10 17:51:45' },
  { id: 3, name: '案件诉讼名称03', time: '2024-10-10 17:51:45' },
  { id: 4, name: '案件诉讼名称04', time: '2024-10-10 17:51:45' },
  { id: 5, name: '案件诉讼名称05', time: '2024-10-10 17:51:45' },
  { id: 6, name: '案件诉讼名称06', time: '2024-10-10 17:51:45' },
  { id: 7, name: '案件诉讼名称07', time: '2024-10-10 17:51:45' },
  { id: 8, name: '案件诉讼名称08', time: '2024-10-10 17:51:45' },
  { id: 9, name: '案件诉讼名称09', time: '2024-10-10 17:51:45' },
  { id: 5, name: '案件诉讼名称05', time: '2024-10-10 17:51:45' },
  { id: 6, name: '案件诉讼名称06', time: '2024-10-10 17:51:45' },
  { id: 7, name: '案件诉讼名称07', time: '2024-10-10 17:51:45' },
  { id: 8, name: '案件诉讼名称08', time: '2024-10-10 17:51:45' },
  { id: 9, name: '案件诉讼名称09', time: '2024-10-10 17:51:45' },
])
</script>

<template>
  <div class="history-panel">
    <div class="header fixed-header">
      <div class="title-container">
        <div class="title">
          <img class="icon" src="@/assets/icons/time.png" alt="History Icon" />
          <h2>历史记录</h2>
        </div>
        <button class="new-chat-btn">
          <img src="@/assets/icons/new-chat.svg" alt="New Chat" />
          <span>新建对话</span>
        </button>
      </div>
      <el-input v-model="searchQuery" class="search-input" placeholder="搜索">
        <template #prefix>
          <el-icon><search /></el-icon>
        </template>
      </el-input>
      <el-date-picker
        style="width: 273px"
        v-model="dateRange"
        type="daterange"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="date-picker"
        :prefix-icon="Calendar"
      />
    </div>
    <ul class="history-list scroll-container">
      <li v-for="item in historyItems" :key="item.id" class="history-item">
        <span class="item-name">{{ item.name }}</span>
        <span class="item-time">{{ item.time }}</span>
      </li>
    </ul>
  </div>
</template>

<style scoped>
.history-panel {
  width: 320px;
  height: 100%;
  border-right: 1px solid #f0f1f2;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding: 24px 23px;
  box-sizing: border-box;
}

.header {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #222529;
}

.icon {
  width: 16px;
  height: 16px;
}

h2 {
  margin: 0;
  font-size: inherit;
  font-weight: inherit;
}

.new-chat-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 400;
  color: #0057d9;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.new-chat-btn img {
  width: 15px;
  height: 15px;
}

.search-input {
  margin-top: 8px;
}

.search-input :deep(.el-input__wrapper),
.date-picker :deep(.el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #d8d8d8;
  box-shadow: none;
}

.date-picker {
  width: 100%;
}

.el-icon {
  color: #7f8792;
}

.history-list {
  list-style: none;
  padding: 0;
  margin: 16px 0 0;
  display: flex;
  flex-direction: column;
}

.history-item {
  border-top: 1px solid #f0f1f2;
  background-color: #ffffff;
  padding: 11px 7px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-bottom: -1px; /* To overlap borders */
}

.history-item:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.history-item:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  margin-bottom: 0;
}

.history-item:hover {
  background-color: #f8f9fa;
}

.item-name {
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  color: #222529;
  white-space: pre;
}

.item-time {
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  color: #7f8792;
  white-space: pre;
}
</style>
