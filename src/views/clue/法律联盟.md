---
title: 法律联盟
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 法律联盟

Base URLs:

# Authentication

* API Key (Authorization)
    - Parameter Name: **Authorization**, in: header. 

- HTTP Authentication, scheme: bearer

# 用户 APP - 法律文书检查任务

<a id="opIdupdateCheckTask"></a>

## PUT 更新法律文书检查任务

PUT /app-api/law/check/task/update

> Body 请求参数

```json
{
  "id": 26969,
  "conversationId": 32187,
  "fileId": 28798,
  "fileUrl": "http://**************:9000/law-prod/law-app/20250726/微信图片_20250711192234_1753522075399.png",
  "userId": 25147,
  "type": 1,
  "status": 2,
  "resultFileId": 25648,
  "resultFileUrl": "http://**************:9000/law-prod/law-app/20250726/微信图片_20250711192234_1753522075399.png"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[CheckTaskSaveReqVO](#schemachecktasksavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIdsubmit"></a>

## POST 提交任务

POST /app-api/law/check/task/submit

> Body 请求参数

```json
{
  "id": 30772
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[CheckTaskSaveReqVO](#schemachecktasksavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIdcreateCheckTask"></a>

## POST 创建法律文书检查任务

POST /app-api/law/check/task/create

> Body 请求参数

```json
{
  "conversationId": 30171,
  "fileId": 28798,
  "fileUrl": "http://**************:9000/law-prod/law-app/20250726/微信图片_20250711192234_1753522075399.png"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[CheckTaskSaveReqVO](#schemachecktasksavereqvo)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"data":0,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultLong](#schemacommonresultlong)|

<a id="opIdgetCheckTaskPage"></a>

## GET 获得法律文书检查任务分页

GET /app-api/law/check/task/page

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|conversationId|query|integer(int64)| 否 |对话编号|
|fileId|query|integer(int64)| 否 |文件id|
|userId|query|integer(int64)| 否 |用户编号|
|type|query|string| 否 |消息类型|
|status|query|integer(int32)| 否 |状态：0-待提交，10-检查中，20-检测完成|
|resultFileId|query|integer(int64)| 否 |检查结果文件id|
|createTime|query|array[string]| 否 |创建时间|
|pageNo|query|integer(int32)| 是 |页码，从 1 开始|
|pageSize|query|integer(int32)| 是 |每页条数，最大值为 100|

> 返回示例

> 200 Response

```
{"code":0,"data":{"list":[{"id":26969,"conversationId":32187,"fileId":28798,"userId":25147,"type":1,"status":2,"resultFileId":25648,"createTime":"2019-08-24T14:15:22Z"}],"total":0},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultPageResultCheckTaskRespVO](#schemacommonresultpageresultchecktaskrespvo)|

<a id="opIdgetCheckTaskList"></a>

## GET 获得任务列表

GET /app-api/law/check/task/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|conversationId|query|integer(int64)| 否 |对话编号|
|fileId|query|integer(int64)| 否 |文件id|
|userId|query|integer(int64)| 否 |用户编号|
|type|query|string| 否 |消息类型|
|status|query|integer(int32)| 否 |状态：0-待提交，10-检查中，20-检测完成|
|resultFileId|query|integer(int64)| 否 |检查结果文件id|
|createTime|query|array[string]| 否 |创建时间|

> 返回示例

> 200 Response

```
{"code":0,"data":[{"id":26969,"conversationId":32187,"fileId":28798,"userId":25147,"type":1,"status":2,"resultFileId":25648,"createTime":"2019-08-24T14:15:22Z"}],"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultListCheckTaskRespVO](#schemacommonresultlistchecktaskrespvo)|

<a id="opIdgetCheckTask"></a>

## GET 获得法律文书检查任务

GET /app-api/law/check/task/get

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |编号|

> 返回示例

> 200 Response

```
{"code":0,"data":{"id":26969,"conversationId":32187,"fileId":28798,"userId":25147,"type":1,"status":2,"resultFileId":25648,"createTime":"2019-08-24T14:15:22Z"},"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultCheckTaskRespVO](#schemacommonresultchecktaskrespvo)|

<a id="opIdexportCheckTaskExcel"></a>

## GET 导出法律文书检查任务 Excel

GET /app-api/law/check/task/export-excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|conversationId|query|integer(int64)| 否 |对话编号|
|fileId|query|integer(int64)| 否 |文件id|
|userId|query|integer(int64)| 否 |用户编号|
|type|query|string| 否 |消息类型|
|status|query|integer(int32)| 否 |状态：0-待提交，10-检查中，20-检测完成|
|resultFileId|query|integer(int64)| 否 |检查结果文件id|
|createTime|query|array[string]| 否 |创建时间|
|pageNo|query|integer(int32)| 是 |页码，从 1 开始|
|pageSize|query|integer(int32)| 是 |每页条数，最大值为 100|

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|None|

<a id="opIddeleteCheckTask"></a>

## DELETE 删除法律文书检查任务

DELETE /app-api/law/check/task/delete

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |编号|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

<a id="opIddeleteCheckTaskList"></a>

## DELETE 批量删除法律文书检查任务

DELETE /app-api/law/check/task/delete-list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|ids|query|array[integer]| 是 |编号|

> 返回示例

> 200 Response

```
{"code":0,"data":true,"msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultBoolean](#schemacommonresultboolean)|

# 数据模型

<h2 id="tocS_CommonResultBoolean">CommonResultBoolean</h2>

<a id="schemacommonresultboolean"></a>
<a id="schema_CommonResultBoolean"></a>
<a id="tocScommonresultboolean"></a>
<a id="tocscommonresultboolean"></a>

```json
{
  "code": 0,
  "data": true,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|boolean|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CheckTaskSaveReqVO">CheckTaskSaveReqVO</h2>

<a id="schemachecktasksavereqvo"></a>
<a id="schema_CheckTaskSaveReqVO"></a>
<a id="tocSchecktasksavereqvo"></a>
<a id="tocschecktasksavereqvo"></a>

```json
{
  "id": 26969,
  "conversationId": 32187,
  "fileId": 28798,
  "fileUrl": "http://**************:9000/law-prod/law-app/20250726/微信图片_20250711192234_1753522075399.png",
  "userId": 25147,
  "type": 1,
  "status": 2,
  "resultFileId": 25648,
  "resultFileUrl": "http://**************:9000/law-prod/law-app/20250726/微信图片_20250711192234_1753522075399.png"
}

```

管理后台 - 法律文书检查任务新增/修改 Request VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||消息编号|
|conversationId|integer(int64)|true|none||对话编号|
|fileId|integer(int64)|false|none||文件id|
|fileUrl|string|false|none||文件url|
|userId|integer(int64)|true|none||用户编号|
|type|string|false|none||任务类型|
|status|integer(int32)|true|none||状态：0-待提交，10-待审核, 20-分析中，30-检测完成，40-驳回|
|resultFileId|integer(int64)|false|none||检查结果文件id|
|resultFileUrl|string|false|none||检查结果文件url|

<h2 id="tocS_CommonResultLong">CommonResultLong</h2>

<a id="schemacommonresultlong"></a>
<a id="schema_CommonResultLong"></a>
<a id="tocScommonresultlong"></a>
<a id="tocscommonresultlong"></a>

```json
{
  "code": 0,
  "data": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|integer(int64)|false|none||none|
|msg|string|false|none||none|

<h2 id="tocS_CheckTaskRespVO">CheckTaskRespVO</h2>

<a id="schemachecktaskrespvo"></a>
<a id="schema_CheckTaskRespVO"></a>
<a id="tocSchecktaskrespvo"></a>
<a id="tocschecktaskrespvo"></a>

```json
{
  "id": 26969,
  "conversationId": 32187,
  "fileId": 28798,
  "userId": 25147,
  "type": 1,
  "status": 2,
  "resultFileId": 25648,
  "createTime": "2019-08-24T14:15:22Z"
}

```

管理后台 - 法律文书检查任务 Response VO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||消息编号|
|conversationId|integer(int64)|true|none||对话编号|
|fileId|integer(int64)|false|none||文件id|
|userId|integer(int64)|true|none||用户编号|
|type|string|false|none||消息类型|
|status|integer(int32)|true|none||状态：0-待提交，10-检查中，20-检测完成|
|resultFileId|integer(int64)|false|none||检查结果文件id|
|createTime|string(date-time)|false|none||创建时间|

<h2 id="tocS_CommonResultPageResultCheckTaskRespVO">CommonResultPageResultCheckTaskRespVO</h2>

<a id="schemacommonresultpageresultchecktaskrespvo"></a>
<a id="schema_CommonResultPageResultCheckTaskRespVO"></a>
<a id="tocScommonresultpageresultchecktaskrespvo"></a>
<a id="tocscommonresultpageresultchecktaskrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 26969,
        "conversationId": 32187,
        "fileId": 28798,
        "userId": 25147,
        "type": 1,
        "status": 2,
        "resultFileId": 25648,
        "createTime": "2019-08-24T14:15:22Z"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[PageResultCheckTaskRespVO](#schemapageresultchecktaskrespvo)|false|none||分页结果|
|msg|string|false|none||none|

<h2 id="tocS_PageResultCheckTaskRespVO">PageResultCheckTaskRespVO</h2>

<a id="schemapageresultchecktaskrespvo"></a>
<a id="schema_PageResultCheckTaskRespVO"></a>
<a id="tocSpageresultchecktaskrespvo"></a>
<a id="tocspageresultchecktaskrespvo"></a>

```json
{
  "list": [
    {
      "id": 26969,
      "conversationId": 32187,
      "fileId": 28798,
      "userId": 25147,
      "type": 1,
      "status": 2,
      "resultFileId": 25648,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "total": 0
}

```

分页结果

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[CheckTaskRespVO](#schemachecktaskrespvo)]|true|none||数据|
|total|integer(int64)|true|none||总量|

<h2 id="tocS_CommonResultCheckTaskRespVO">CommonResultCheckTaskRespVO</h2>

<a id="schemacommonresultchecktaskrespvo"></a>
<a id="schema_CommonResultCheckTaskRespVO"></a>
<a id="tocScommonresultchecktaskrespvo"></a>
<a id="tocscommonresultchecktaskrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 26969,
    "conversationId": 32187,
    "fileId": 28798,
    "userId": 25147,
    "type": 1,
    "status": 2,
    "resultFileId": 25648,
    "createTime": "2019-08-24T14:15:22Z"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[CheckTaskRespVO](#schemachecktaskrespvo)|false|none||管理后台 - 法律文书检查任务 Response VO|
|msg|string|false|none||none|

<h2 id="tocS_CommonResultListCheckTaskRespVO">CommonResultListCheckTaskRespVO</h2>

<a id="schemacommonresultlistchecktaskrespvo"></a>
<a id="schema_CommonResultListCheckTaskRespVO"></a>
<a id="tocScommonresultlistchecktaskrespvo"></a>
<a id="tocscommonresultlistchecktaskrespvo"></a>

```json
{
  "code": 0,
  "data": [
    {
      "id": 26969,
      "conversationId": 32187,
      "fileId": 28798,
      "userId": 25147,
      "type": 1,
      "status": 2,
      "resultFileId": 25648,
      "createTime": "2019-08-24T14:15:22Z"
    }
  ],
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|[[CheckTaskRespVO](#schemachecktaskrespvo)]|false|none||[管理后台 - 法律文书检查任务 Response VO]|
|msg|string|false|none||none|

