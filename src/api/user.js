import request from '@/utils/request'

// ==================== 用户认证相关接口 ====================

/**
 * 使用手机号和密码登录
 * @param {object} data - 包含 mobile 和 password 的对象
 * @returns {Promise}
 */
export function login(data) {
  return request.post('/app-api/member/auth/login', data)
}

// ==================== 用户个人中心相关接口 ====================

/**
 * 获得登录用户信息
 * @returns {Promise}
 */
export function getUserProfile() {
  return request.get('/admin-api/system/user/profile/get')
}

/**
 * 修改用户个人信息
 * @param {object} data - 用户信息
 * @param {string} data.nickname - 用户昵称
 * @param {string} data.email - 用户邮箱
 * @param {string} data.mobile - 手机号码
 * @param {number} data.sex - 用户性别（1-男，2-女）
 * @param {string} data.avatar - 用户头像
 * @returns {Promise}
 */
export function updateUserProfile(data) {
  return request.put('/admin-api/system/user/profile/update', data)
}

/**
 * 修改用户个人密码
 * @param {object} data - 密码信息
 * @param {string} data.oldPassword - 旧密码
 * @param {string} data.newPassword - 新密码
 * @returns {Promise}
 */
export function updateUserPassword(data) {
  return request.put('/admin-api/system/user/profile/update-password', data)
}
