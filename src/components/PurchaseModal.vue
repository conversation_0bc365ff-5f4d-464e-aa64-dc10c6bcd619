<template>
  <div class="purchase-modal" @click.stop>
    <!-- 头部用户信息 -->
    <div class="purchase-header">
      <div class="user-info">
        <div class="avatar-container">
          <img
            v-if="userInfo.avatar"
            class="avatar"
            :src="userInfo.avatar"
            alt="用户头像"
            @error="handleAvatarError"
          />
          <div v-else class="avatar-placeholder">
            {{ getFirstChar(userInfo.name) }}
          </div>
        </div>
        <div class="user-details">
          <span class="username">{{ userInfo.name }}</span>
          <span class="remaining-count">剩余可下载次数 {{ userInfo.remainingCount }}</span>
        </div>
      </div>
      <button class="close-btn" @click="handleClose">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M12 4L4 12M4 4L12 12"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          />
        </svg>
      </button>
    </div>

    <div class="purchase-content">
      <!-- 左侧套餐选择 -->
      <div class="packages-section">
        <div class="section-header">
          <span class="title">购买次数套餐</span>
          <div class="tip">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
              <circle cx="7" cy="7" r="6" stroke="#7f8792" stroke-width="1" />
              <path d="M7 4v3M7 10h.01" stroke="#7f8792" stroke-width="1" stroke-linecap="round" />
            </svg>
            <span class="tip-text">您可以选择设置好的套餐，也可以单独购买</span>
          </div>
        </div>

        <!-- 套餐网格 -->
        <div class="packages-grid">
          <div
            v-for="pkg in packages"
            :key="pkg.id"
            class="package-card"
            :class="{ selected: selectedPackageId === pkg.id }"
            @click="selectPackage(pkg)"
          >
            <span class="package-title">下载次数：{{ pkg.count }}次</span>
            <div class="package-price">
              <span class="price-symbol">≈¥</span>
              <span class="price-amount">{{ pkg.pricePerUnit }}</span>
              <span class="price-unit">/次</span>
            </div>
          </div>
        </div>

        <!-- 加购次数 -->
        <div class="additional-section">
          <span class="section-title">加购次数</span>
          <div class="quantity-control">
            <button class="quantity-btn" @click="decreaseQuantity">
              <div class="minus-icon"></div>
            </button>
            <div class="quantity-input">
              <span class="quantity-value">{{ additionalQuantity }}</span>
            </div>
            <button class="quantity-btn plus" @click="increaseQuantity">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path
                  d="M6 1v10M1 6h10"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 右侧支付信息 -->
      <div class="payment-section">
        <div class="total-price">
          <span class="currency">¥</span>
          <span class="amount">{{ totalAmount }}</span>
        </div>
        <span class="package-summary">套餐：下载次数 {{ selectedPackage?.count || 0 }}次</span>

        <div class="qr-placeholder">
          <div class="qr-code-area">
            <svg width="180" height="180" viewBox="0 0 180 180" fill="none">
              <rect width="180" height="180" fill="#f0f0f0" rx="8" />
              <text x="90" y="95" text-anchor="middle" font-size="14" fill="#999">支付二维码</text>
            </svg>
          </div>
        </div>

        <span class="payment-methods">可使用 微信/支付宝 扫码支付</span>

        <div class="agreement">
          <span class="agreement-text">支付即视为你同意</span>
          <span class="agreement-link">相关协议</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  userInfo: {
    type: Object,
    default: () => ({
      name: '阿琦',
      avatar: '',
      remainingCount: 0,
    }),
  },
})

const emit = defineEmits(['close', 'purchase'])

// 套餐数据
const packages = ref([
  { id: 1, count: 10, pricePerUnit: 100, totalPrice: 1000 },
  { id: 2, count: 20, pricePerUnit: 80, totalPrice: 1600 },
  { id: 3, count: 40, pricePerUnit: 60, totalPrice: 2400 },
  { id: 4, count: 50, pricePerUnit: 50, totalPrice: 2500 },
])

// 选中的套餐ID
const selectedPackageId = ref(1) // 默认选中第一个套餐

// 计算选中的套餐对象
const selectedPackage = computed(() => {
  return packages.value.find((pkg) => pkg.id === selectedPackageId.value)
})

// 加购数量
const additionalQuantity = ref(20)

// 计算总金额
const totalAmount = computed(() => {
  if (!selectedPackage.value) return '0.00'
  const basePrice = selectedPackage.value.totalPrice
  const additionalPrice = additionalQuantity.value * selectedPackage.value.pricePerUnit
  return (basePrice + additionalPrice).toFixed(2)
})

// 选择套餐
const selectPackage = (pkg) => {
  selectedPackageId.value = pkg.id
}

// 增加数量
const increaseQuantity = () => {
  additionalQuantity.value += 1
}

// 减少数量
const decreaseQuantity = () => {
  if (additionalQuantity.value > 0) {
    additionalQuantity.value -= 1
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
}

// 处理购买
const handlePurchase = () => {
  const purchaseData = {
    package: selectedPackage.value,
    additionalQuantity: additionalQuantity.value,
    totalAmount: totalAmount.value,
  }
  emit('purchase', purchaseData)
}

// 获取用户名第一个字符
const getFirstChar = (name) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

// 处理头像加载错误
const handleAvatarError = (event) => {
  // 当头像加载失败时，隐藏 img 元素，显示占位符
  event.target.style.display = 'none'
}

// 暴露方法供父组件调用
defineExpose({
  handlePurchase,
})
</script>

<style scoped>
/* 购买界面样式 */
.purchase-modal {
  width: 880px;
  height: auto;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 头部样式 */
.purchase-header {
  width: 100%;
  height: 72px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px;
  background: linear-gradient(270deg, #ffeac2 2.14%, #fff7e7 100%);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-container {
  width: 36px;
  height: 36px;
  position: relative;
  background-color: #ff5827;
  border-radius: 6px;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background-color: #ff5827;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.avatar-placeholder:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.username {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 500;
  line-height: 16px;
  color: #222529;
}

.remaining-count {
  font-size: 13px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  line-height: 15px;
  color: #414447;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  cursor: pointer;
  color: #7f8792;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: rgba(127, 135, 146, 0.1);
  color: #222529;
  transform: scale(1.05);
}

.close-btn:active {
  transform: scale(0.95);
}

/* 内容区域 */
.purchase-content {
  display: flex;
  padding: 0;
  gap: 0;
  height: 600px;
}

.packages-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title {
  font-size: 16px;
  font-family: 'HarmonyOS Sans';
  font-weight: 500;
  line-height: 19px;
  color: #222529;
}

.tip {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tip-icon {
  width: 14px;
  height: 14px;
}

.tip-text {
  font-size: 13px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  line-height: 15px;
  color: #7f8792;
}

/* 套餐网格 */
.packages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.package-card {
  width: 280px;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 26px 88px;
  gap: 20px;
  border-radius: 12px;
  border: 1px solid #dcdcdc;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.package-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(238, 175, 97, 0.1), transparent);
  transition: left 0.5s ease;
}

.package-card:hover {
  border-color: #eeaf61;
  box-shadow: 0 4px 12px rgba(238, 175, 97, 0.2);
  transform: translateY(-2px);
}

.package-card:hover::before {
  left: 100%;
}

.package-card:active {
  transform: translateY(0);
}

/* 选中状态样式 */
.package-card.selected {
  border-color: #eeaf61;
  background: linear-gradient(180deg, #fff5dd 0%, #fff0d0 100%);
  box-shadow: 0 6px 16px rgba(238, 175, 97, 0.3);
  transform: translateY(-3px);
}

.package-card.selected::after {
  content: '✓';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #eeaf61;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  animation: checkmark 0.3s ease;
}

@keyframes checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.package-card.selected .package-title {
  color: #6b3a00;
}

.package-card.selected .price-symbol,
.package-card.selected .price-amount,
.package-card.selected .price-unit {
  color: #6b3a00;
}

.package-title {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 500;
  line-height: 16px;
  color: #222529;
  text-align: center;
}

.package-price {
  display: flex;
  align-items: baseline;
  text-align: center;
  line-height: 16px;
}

.price-symbol {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  color: #222529;
}

.price-amount {
  font-size: 24px;
  font-family: 'HarmonyOS Sans';
  font-weight: 700;
  line-height: 28px;
  color: #222529;
}

.price-unit {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  color: #222529;
}

/* 加购次数 */
.additional-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 16px;
  font-family: 'HarmonyOS Sans';
  font-weight: 500;
  line-height: 19px;
  color: #222529;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 170px;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #dcdcdc;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.quantity-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 87, 217, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition:
    width 0.3s ease,
    height 0.3s ease;
}

.quantity-btn:active::before {
  width: 40px;
  height: 40px;
}

.quantity-btn:hover {
  background-color: #f1f2f4;
  border-color: #0057d9;
  transform: scale(1.05);
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-btn:first-child {
  background-color: #f1f2f4;
  border: none;
}

.quantity-btn:first-child:hover {
  background-color: #e6e6e6;
}

.minus-icon {
  width: 12px;
  height: 2px;
  background-color: #b4b8bf;
}

.quantity-input {
  flex: 1;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #dcdcdc;
  background-color: #ffffff;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.quantity-input:focus-within {
  border-color: #0057d9;
  box-shadow: 0 0 0 2px rgba(0, 87, 217, 0.1);
}

.quantity-value {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 500;
  line-height: 16px;
  color: #222529;
  transition: all 0.2s ease;
  animation: numberChange 0.3s ease;
}

@keyframes numberChange {
  0% {
    transform: scale(1.2);
    color: #0057d9;
  }
  100% {
    transform: scale(1);
    color: #222529;
  }
}

/* 右侧支付区域 */
.payment-section {
  width: 280px;
  border-left: 1px solid #dcdcdc;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 58px 24px 24px;
  gap: 20px;
}

.total-price {
  display: flex;
  align-items: baseline;
  position: relative;
}

.currency {
  font-size: 18px;
  font-family: 'HarmonyOS Sans';
  font-weight: 500;
  line-height: 21px;
  color: #f5222d;
  transition: all 0.3s ease;
}

.amount {
  font-size: 36px;
  font-family: 'HarmonyOS Sans';
  font-weight: 700;
  line-height: 42px;
  color: #f5222d;
}

.package-summary {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  line-height: 16px;
  color: #f5222d;
  text-align: center;
}

.qr-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qr-code-area {
  width: 180px;
  height: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.payment-methods {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  line-height: 16px;
  color: #7f8792;
  text-align: center;
}

.agreement {
  display: flex;
  text-align: center;
  line-height: 16px;
}

.agreement-text {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  color: #7f8792;
}

.agreement-link {
  font-size: 14px;
  font-family: 'HarmonyOS Sans';
  font-weight: 400;
  color: #0057d9;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.agreement-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: #0057d9;
  transition: width 0.3s ease;
}

.agreement-link:hover {
  color: #004bb8;
  transform: translateY(-1px);
}

.agreement-link:hover::after {
  width: 100%;
}
</style>
