---
title: 法律联盟
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 法律联盟

Base URLs:

# Authentication

* API Key (Authorization)
    - Parameter Name: **Authorization**, in: header. 

# 用户 App - 文件存储

<a id="opIduploadFile"></a>

## POST 上传文件

POST /app-api/infra/file/upload

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|directory|query|string| 否 |文件目录|

> 返回示例

> 200 Response

```
{"code":0,"data":"string","msg":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResultString](#schemacommonresultstring)|

# 数据模型

<h2 id="tocS_CommonResultString">CommonResultString</h2>

<a id="schemacommonresultstring"></a>
<a id="schema_CommonResultString"></a>
<a id="tocScommonresultstring"></a>
<a id="tocscommonresultstring"></a>

```json
{
  "code": 0,
  "data": "string",
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||none|
|data|string|false|none||none|
|msg|string|false|none||none|

