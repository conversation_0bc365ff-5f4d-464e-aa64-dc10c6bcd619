<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElLoading } from 'element-plus'
import { uploadFile } from '@/api/checkTask'

const userStore = useUserStore()

const profile = ref({
  avatar: '',
  nickname: '',
  userId: '',
  phone: '',
  email: '',
})

const originalProfile = ref({})
const loading = ref(false)
const saving = ref(false)

const showFallbackAvatar = computed(() => !profile.value.avatar)
const avatarInitial = computed(() =>
  profile.value.nickname ? profile.value.nickname.charAt(0) : 'U',
)

// 获取用户信息
const fetchUserProfile = async () => {
  try {
    loading.value = true
    await userStore.fetchUserProfile()
    const userInfo = userStore.userInfo

    profile.value = {
      avatar: userInfo.avatar || '',
      nickname: userInfo.nickname || '',
      userId: userInfo.id || userInfo.username || '',
      phone: userInfo.mobile || '',
      email: userInfo.email || '',
    }

    // 保存原始数据用于取消操作
    originalProfile.value = { ...profile.value }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 上传头像
const handleAvatarUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 JPG、PNG、GIF 格式的图片')
    return
  }

  // 验证文件大小 (2MB)
  if (file.size > 2 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 2MB')
    return
  }

  try {
    const loadingInstance = ElLoading.service({ text: '上传头像中...' })
    const response = await uploadFile(file, 'avatar')
    profile.value.avatar = response.data
    ElMessage.success('头像上传成功')
    loadingInstance.close()
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
  }
}

// 触发文件选择
const triggerAvatarUpload = () => {
  document.getElementById('avatar-upload').click()
}

// 保存用户信息
const saveProfile = async () => {
  try {
    saving.value = true

    const updateData = {
      nickname: profile.value.nickname,
      email: profile.value.email,
      mobile: profile.value.phone,
      avatar: profile.value.avatar,
    }

    await userStore.updateProfile(updateData)
    originalProfile.value = { ...profile.value }
  } catch (error) {
    console.error('保存用户信息失败:', error)
  } finally {
    saving.value = false
  }
}

// 取消编辑
const cancelEdit = () => {
  profile.value = { ...originalProfile.value }
}

// 检查是否有修改
const hasChanges = computed(() => {
  return JSON.stringify(profile.value) !== JSON.stringify(originalProfile.value)
})

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserProfile()
})
</script>

<template>
  <div class="user-profile-container" v-loading="loading">
    <div class="avatar-section">
      <div class="avatar-container">
        <img
          v-if="!showFallbackAvatar"
          :src="profile.avatar"
          alt="User Avatar"
          class="avatar-image"
        />
        <div v-else class="avatar-fallback">
          {{ avatarInitial }}
        </div>
      </div>
      <button class="upload-button" @click="triggerAvatarUpload">上传头像</button>
      <input
        id="avatar-upload"
        type="file"
        accept="image/*"
        style="display: none"
        @change="handleAvatarUpload"
      />
    </div>

    <form class="profile-form">
      <div class="form-group">
        <label for="nickname">昵称</label>
        <input type="text" id="nickname" v-model="profile.nickname" placeholder="请输入昵称" />
      </div>
      <div class="form-group">
        <label for="userId">用户ID</label>
        <input type="text" id="userId" v-model="profile.userId" readonly />
      </div>
      <div class="form-group">
        <label for="phone">手机号码</label>
        <input type="tel" id="phone" v-model="profile.phone" placeholder="请输入手机号码" />
      </div>
      <div class="form-group">
        <label for="email">邮箱地址</label>
        <input type="email" id="email" v-model="profile.email" placeholder="请输入邮箱地址" />
      </div>
    </form>

    <div class="action-buttons">
      <button class="save-button" :disabled="saving || !hasChanges" @click="saveProfile">
        {{ saving ? '保存中...' : '保存' }}
      </button>
      <button class="cancel-button" :disabled="!hasChanges" @click="cancelEdit">取消</button>
    </div>
  </div>
</template>

<style scoped>
.user-profile-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  background-color: #ff5827;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 500;
}

.upload-button {
  padding: 8px 16px;
  border-radius: 217px;
  border: 1px solid #0057d9;
  background-color: #ffffff;
  color: #0057d9;
  font-size: 14px;
  cursor: pointer;
}

.profile-form {
  display: grid;
  grid-template-columns: repeat(2, 280px);
  gap: 24px;
  margin-top: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group label {
  font-size: 14px;
  color: #595e66;
}

.form-group input {
  height: 36px;
  border-radius: 6px;
  border: 1px solid #dcdcdc;
  background-color: #ffffff;
  padding: 0 12px;
  font-size: 14px;
  color: #222529;
  width: 100%;
}

.form-group input[readonly] {
  background-color: #f4f4f4;
  color: #595e66;
  border: 1px solid #dcdcdc;
  cursor: not-allowed;
}

.form-group input[readonly]:focus {
  outline: none;
  border: 1px solid #dcdcdc;
  box-shadow: none;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.save-button,
.cancel-button {
  width: 100px;
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.save-button {
  background-color: #0057d9;
  color: #ffffff;
  border: none;
}

.save-button:disabled {
  background-color: #0057d9;
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-button {
  background-color: #ffffff;
  color: #222529;
  border: 1px solid #d8d8d8;
}
</style>
